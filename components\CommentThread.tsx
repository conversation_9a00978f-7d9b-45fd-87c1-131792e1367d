import React, { useState } from 'react';
import { Comment, User } from '../types';
import { useNavigate } from 'react-router-dom';
import HeartIcon from './icons/HeartIcon';
import TrashIcon from './icons/TrashIcon';
import ReplyIcon from './icons/ReplyIcon';
import ChevronDownIcon from './icons/ChevronDownIcon';
import ChevronRightIcon from './icons/ChevronRightIcon';
import { timeSince } from '../utils/dateUtils';

interface CommentThreadProps {
  comment: Comment;
  postId: string;
  currentUser: User | null;
  isAdmin: boolean;
  onReply: (parentId: string, replyToUsername: string) => void;
  onDelete: (commentId: string) => Promise<void>;
  onToggleLike: (commentId: string) => Promise<void>;
  depth?: number;
  maxDepth?: number;
}

const CommentThread: React.FC<CommentThreadProps> = ({
  comment,
  postId,
  currentUser,
  isAdmin,
  onReply,
  onDelete,
  onToggleLike,
  depth = 0,
  maxDepth = 5
}) => {
  const navigate = useNavigate();
  const [isCollapsed, setIsCollapsed] = useState(false);
  const hasReplies = comment.replies && comment.replies.length > 0;
  const isMaxDepth = depth >= maxDepth;

  // Ensure replies array exists for safe rendering
  const replies = comment.replies || [];

  // Calculate indentation and styling based on depth
  const indentationClass = depth > 0 ? `ml-${Math.min(depth * 4, 16)}` : '';
  const borderClass = depth > 0 ? 'border-l-2 border-neutral-border pl-3' : '';
  const bgOpacity = Math.max(0.1, 1 - (depth * 0.1));

  const handleReplyClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    onReply(comment.id, comment.username);
  };

  const handleDeleteClick = async (e: React.MouseEvent) => {
    e.stopPropagation();
    try {
      await onDelete(comment.id);
    } catch (error) {
      console.error('Error deleting comment:', error);
      alert('Failed to delete comment. Please try again.');
    }
  };

  const handleLikeClick = async (e: React.MouseEvent) => {
    e.stopPropagation();
    if (!currentUser) return;
    try {
      await onToggleLike(comment.id);
    } catch (error) {
      console.error('Error toggling comment like:', error);
      alert('Failed to like comment. Please try again.');
    }
  };

  const handleUserClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    navigate(`/user/${comment.userId}`);
  };

  const toggleCollapse = (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsCollapsed(!isCollapsed);
  };

  return (
    <div className={`${indentationClass} ${borderClass}`}>
      {/* Main Comment */}
      <div
        className="flex items-start space-x-1.5 text-sm mb-1.5 mobile-comment-thread comment-thread-compact"
        style={{ backgroundColor: `rgba(45, 45, 45, ${bgOpacity})` }}
      >
        {/* Avatar */}
        <button
          className="w-5 h-5 rounded-full mt-0.5 border border-neutral-border hover-scale transition-transform duration-200 overflow-hidden cursor-pointer hover:border-brand-primary flex-shrink-0"
          onClick={handleUserClick}
          title={`View ${comment.username}'s profile`}
        >
          <img src={comment.avatarUrl} alt={comment.username} className="w-full h-full object-cover" />
        </button>

        {/* Comment Content */}
        <div className="flex-grow bg-neutral-base p-1.5 rounded-md mobile-comment-content">
          <div className="flex justify-between items-center">
            <div className="flex items-center space-x-1.5">
              <button
                className="font-medium text-neutral-100 hover:text-brand-primary transition-colors duration-200 text-left text-xs comment-username"
                onClick={handleUserClick}
                title={`View ${comment.username}'s profile`}
              >
                {comment.username}
              </button>
              {comment.replyToUsername && (
                <span className="text-xs text-brand-primary">
                  → @{comment.replyToUsername}
                </span>
              )}
            </div>
            <div className="flex items-center space-x-1">
              {hasReplies && (
                <button
                  onClick={toggleCollapse}
                  className="text-neutral-muted hover:text-brand-primary transition-colors duration-200 p-0.5"
                  title={isCollapsed ? "Expand replies" : "Collapse replies"}
                >
                  {isCollapsed ? (
                    <ChevronRightIcon className="w-3 h-3" />
                  ) : (
                    <ChevronDownIcon className="w-3 h-3" />
                  )}
                </button>
              )}
              <span className="text-xs text-neutral-muted">{timeSince(comment.timestamp)}</span>
            </div>
          </div>

          <p className="text-neutral-200 mt-0.5 text-sm whitespace-pre-wrap leading-tight comment-text-compact">{comment.text}</p>

          {/* Comment Actions */}
          <div className="flex items-center justify-between mt-1.5 comment-actions-compact">
            <div className="flex items-center space-x-2">
              {/* Like Button */}
              {currentUser && (
                <button
                  onClick={handleLikeClick}
                  className={`flex items-center space-x-0.5 hover:text-red-500 transition-all duration-200 hover-scale text-xs ${
                    comment.isLikedByCurrentUser ? 'text-red-500 animate-pulse-glow' : 'text-neutral-muted'
                  }`}
                  title={comment.isLikedByCurrentUser ? "Unlike comment" : "Like comment"}
                >
                  <HeartIcon className="w-3 h-3" isFilled={comment.isLikedByCurrentUser} />
                  <span>{comment.likes || 0}</span>
                </button>
              )}

              {/* Reply Button */}
              {currentUser && !isMaxDepth && (
                <button
                  onClick={handleReplyClick}
                  className="flex items-center space-x-0.5 text-neutral-muted hover:text-brand-primary transition-all duration-200 hover-scale text-xs"
                  title="Reply to comment"
                >
                  <ReplyIcon className="w-3 h-3" />
                  <span>Reply</span>
                </button>
              )}

              {/* Reply Count */}
              {hasReplies && (
                <span className="text-xs text-neutral-muted">
                  {replies.length} {replies.length === 1 ? 'reply' : 'replies'}
                </span>
              )}
            </div>

            {/* Delete Button */}
            {(isAdmin || currentUser?.id === comment.userId) && (
              <button
                onClick={handleDeleteClick}
                className="text-accent-error hover:text-red-400 p-0.5 rounded-md transition-colors"
                title="Delete Comment"
              >
                <TrashIcon className="w-3 h-3" />
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Nested Replies */}
      {hasReplies && !isCollapsed && (
        <div className="mt-1.5 space-y-1.5">
          {replies.map(reply => (
            <CommentThread
              key={reply.id}
              comment={reply}
              postId={postId}
              currentUser={currentUser}
              isAdmin={isAdmin}
              onReply={onReply}
              onDelete={onDelete}
              onToggleLike={onToggleLike}
              depth={depth + 1}
              maxDepth={maxDepth}
            />
          ))}
        </div>
      )}
    </div>
  );
};

export default CommentThread;
