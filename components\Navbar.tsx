
import React, { useState, useEffect } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { APP_NAME } from '../constants';
import HomeIcon from './icons/HomeIcon';
import PlusCircleIcon from './icons/PlusCircleIcon';
import UserCircleIcon from './icons/UserCircleIcon';
import AdminIcon from './icons/AdminIcon';
import MessagesIcon from './icons/MessagesIcon';
import { useAuth } from '../hooks/useAuth';
import { getTotalUnreadCount } from '../services/firebaseService';

import ContactAdmin from './ContactAdmin';

const Navbar: React.FC = () => {
  const { currentUser, logout, isAdmin } = useAuth();
  const location = useLocation();
  const navigate = useNavigate();
  const [showContactAdmin, setShowContactAdmin] = useState(false);
  const [unreadCount, setUnreadCount] = useState(0);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  // Check for unread messages periodically
  useEffect(() => {
    if (!currentUser) {
      setUnreadCount(0);
      return;
    }

    const checkUnreadMessages = async () => {
      try {
        const count = await getTotalUnreadCount(currentUser.id);
        setUnreadCount(count);
      } catch (error) {
        console.error('Error checking unread messages:', error);
      }
    };

    // Check immediately
    checkUnreadMessages();

    // Check every 30 seconds
    const interval = setInterval(checkUnreadMessages, 30000);

    return () => clearInterval(interval);
  }, [currentUser]);

  const navItems = [
    { path: '/', icon: HomeIcon, label: 'Home' },
    { path: '/create', icon: PlusCircleIcon, label: 'Create Post', requiresLogin: true },
    { path: '/profile', icon: UserCircleIcon, label: 'Profile', requiresLogin: true },
  ];

  const handleLogout = () => {
    logout();
    navigate('/login'); // Redirect to login page after logout
  };


  return (
    <nav className="bg-neutral-surface border-b border-neutral-border shadow-lg sticky top-0 z-50 cyber-border">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <Link to="/" className="text-xl sm:text-2xl font-logo font-bold text-brand-primary hover:text-brand-secondary transition-all duration-300 hover-text-glow animate-text-glow">
            {APP_NAME}
          </Link>

          {/* Navigation - Always visible when logged in */}
          {currentUser && (
            <div className="flex items-center space-x-1 sm:space-x-3">
              {navItems.map(item => (
                <Link
                  key={item.path}
                  to={item.path}
                  title={item.label}
                  className={`p-1 sm:p-2 rounded-md transition-all duration-200 hover-scale hover-glow relative ${
                    location.pathname === item.path
                      ? 'text-brand-primary bg-neutral-base animate-pulse-glow'
                      : 'text-neutral-muted hover:text-neutral-100 hover:bg-neutral-base/50'
                  }`}
                >
                  <item.icon className="w-5 h-5 sm:w-6 sm:h-6" />
                </Link>
              ))}
            </div>
          )}

          {/* Right side - Profile and Mobile Menu */}
          <div className="flex items-center space-x-2 sm:space-x-3">
            {currentUser ? (
              <>
                {/* Profile Avatar */}
                <Link to={`/user/${currentUser.id}`} title="View your profile" className="hidden sm:block">
                  <img src={currentUser.avatarUrl} alt={currentUser.username} className="w-8 h-8 rounded-full border-2 border-brand-primary hover-scale transition-transform duration-200 animate-pulse-glow cursor-pointer"/>
                </Link>

                {/* Menu Button */}
                <button
                  onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
                  className="p-2 rounded-md text-neutral-muted hover:text-brand-primary hover:bg-neutral-base/50 transition-all duration-200 hover-scale"
                  title="Menu"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    {isMobileMenuOpen ? (
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    ) : (
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                    )}
                  </svg>
                </button>
              </>
            ) : (
              // Logged out state
              <Link to="/signup" className="text-neutral-muted hover:text-brand-primary text-sm font-medium transition-colors">
                Sign Up
              </Link>
            )}
          </div>
        </div>

        {/* Mobile Menu */}
        {currentUser && isMobileMenuOpen && (
          <div className="border-t border-neutral-border bg-neutral-surface animate-slide-up">
            <div className="px-4 py-3 space-y-2">
              {/* Profile Section */}
              <Link
                to={`/user/${currentUser.id}`}
                className="flex items-center space-x-3 p-3 rounded-md hover:bg-neutral-base/50 transition-all duration-200"
                onClick={() => setIsMobileMenuOpen(false)}
              >
                <img src={currentUser.avatarUrl} alt={currentUser.username} className="w-10 h-10 rounded-full border-2 border-brand-primary"/>
                <div>
                  <p className="text-neutral-100 font-medium">{currentUser.username}</p>
                  <p className="text-neutral-muted text-sm">View Profile</p>
                </div>
              </Link>

              {/* Admin Link */}
              {isAdmin && (
                <Link
                  to="/admin"
                  className={`flex items-center space-x-3 p-3 rounded-md transition-all duration-200 ${
                    location.pathname === '/admin' || location.pathname.startsWith('/admin/')
                      ? 'text-brand-primary bg-neutral-base'
                      : 'text-neutral-muted hover:text-neutral-100 hover:bg-neutral-base/50'
                  }`}
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  <AdminIcon className="w-5 h-5" />
                  <span className="font-medium">Admin Panel</span>
                </Link>
              )}

              {/* Actions */}
              <div className="border-t border-neutral-border pt-3 mt-3 space-y-2">
                <button
                  onClick={() => {
                    setShowContactAdmin(true);
                    setIsMobileMenuOpen(false);
                  }}
                  className="flex items-center space-x-3 p-3 rounded-md text-neutral-muted hover:text-neutral-100 hover:bg-neutral-base/50 transition-all duration-200 w-full text-left"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                  </svg>
                  <span className="font-medium">Contact Admin</span>
                </button>
                <button
                  onClick={() => {
                    handleLogout();
                    setIsMobileMenuOpen(false);
                  }}
                  className="flex items-center space-x-3 p-3 rounded-md text-neutral-muted hover:text-red-400 hover:bg-neutral-base/50 transition-all duration-200 w-full text-left"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                  </svg>
                  <span className="font-medium">Logout</span>
                </button>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Contact Admin Modal */}
      {showContactAdmin && (
        <ContactAdmin onClose={() => setShowContactAdmin(false)} />
      )}
    </nav>
  );
};

export default Navbar;
